#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# 读取原文件
with open('cookie_extractor.py', 'r', encoding='utf-8') as f:
    content = f.read()

# 定义新的平台配置
new_platforms_config = '''        platforms_config = [
            {"name": "头条", "icon": "📰", "value": "头条"},
            {"name": "百家号", "icon": "🅱", "value": "百家"},
            {"name": "抖音", "icon": "🎵", "value": "抖音"},
            {"name": "小红书", "icon": "📖", "value": "小红书"},
            {"name": "快手", "icon": "📹", "value": "快手"},
            {"name": "B站", "icon": "📺", "value": "B站"}
        ]'''

# 查找并替换平台配置部分
lines = content.split('\n')
start_line = -1
end_line = -1

for i, line in enumerate(lines):
    if 'platforms_config = [' in line:
        start_line = i
    if start_line != -1 and line.strip() == ']' and 'platforms_config' in ''.join(lines[max(0, i-10):i]):
        end_line = i
        break

if start_line != -1 and end_line != -1:
    # 替换内容
    new_lines = lines[:start_line] + new_platforms_config.split('\n') + lines[end_line+1:]
    new_content = '\n'.join(new_lines)
    
    # 写回文件
    with open('cookie_extractor.py', 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print('图标已成功更新！')
    print('更新的图标：')
    print('📰 头条 (报纸)')
    print('🅱 百家号 (百度logo)')
    print('🎵 抖音 (音符)')
    print('📖 小红书 (一本书)')
    print('📹 快手 (摄像机)')
    print('📺 B站 (电视机)')
else:
    print('未找到platforms_config配置')
